#include "stdafx.h"

#include <process.h>
#include <fstream>
#include <rapidjson/document.h>
#include <rapidjson/istreamwrapper.h>

#include <ATF/global.hpp>

#include "NexusPro.h"
#include "AddonRegistry.h"
#include "../../Common/Helpers/RapidHelper.hpp"

namespace GameServer
{
    CNexusPro::CNexusPro()
        : m_AtfCoreRegistry(ATF::CATFCoreRegistry::get_instance())
        , m_spModuleRegistry(CModuleRegistry::get_instance())
    {
        m_AtfCoreRegistry.registry();
    }

    void CNexusPro::start()
    {
        std::call_once(m_ofStart, [this] {
            configure();

            m_spModuleRegistry->load();

            ::_beginthread(&CNexusPro::s_routine, 0, (void *)this);
        });
    }

    void CNexusPro::stop()
    {
        std::call_once(m_ofStop, [this] {
            m_spModuleRegistry->unload();

            m_bStop.store(true);
            m_cvCondition.notify_all();
            std::unique_lock<std::mutex> RoutineLock(m_mtxRoutine);
        });
    }

    void CNexusPro::s_routine(void* pObj)
    {
        ((CNexusPro*)(pObj))->routine();
    }

    void CNexusPro::routine()
    {
        std::unique_lock<std::mutex> RoutineLock(m_mtxRoutine);
        
        {
            std::unique_lock<std::mutex> lock(m_mtxCondition);
            while(!m_cvCondition.wait_for(lock, m_timeWaitOpenWorld, [this] {
                return ATF::Global::g_MainThread->m_bWorldOpen || m_bStop.load();
            }));
        }

        m_spModuleRegistry->zone_start();

        for (;!m_bStop.load(); std::this_thread::sleep_for(m_timeStepDelay))
        {
            m_spModuleRegistry->loop();
        }

        RoutineLock.unlock();

        ::_endthreadex(0);
    }

    void CNexusPro::configure()
    {
        std::ifstream ifs(L"./NexusProGS/global.json");

        rapidjson::IStreamWrapper isw(ifs);
        rapidjson::Document GlobalConfig;
        if (GlobalConfig.ParseStream(isw).HasParseError())
        {
            ATF::Global::MyMessageBox("CNexusPro::configure", "Configuration file - corrupted");
            throw std::runtime_error("Configuration file - corrupted");
        }

        const auto&  cfgIntervals = GlobalConfig["intervals"];
        m_timeWaitOpenWorld = std::chrono::milliseconds(
            RapidHelper::GetValue<uint64_t>(cfgIntervals, "open_world_wait"));

        m_timeStepDelay = std::chrono::milliseconds(
            RapidHelper::GetValue<uint64_t>(cfgIntervals, "step_delay"));
        
        m_spModuleRegistry->configure(GlobalConfig["registry"]);
    }

    #define DllExport __declspec(dllexport)
    extern "C" DllExport void NexusProGS()
    {
    }
}
