#pragma once

// This file includes all addon headers to ensure they are registered automatically
// via the CModuleRegister<T> template mechanism

// Include all addon headers
#include "../Addons/AccuracyEffect/AccuracyEffect.h"
#include "../Addons/Advert/advert.h"
#include "../Addons/BonusStart/BonusStart.h"
#include "../Addons/ChatLog/ChatLog.h"
#include "../Addons/DefenceFormula/DefenceFormula.h"
#include "../Addons/EnchantChance/EnchantChance.h"
#include "../Addons/GMCommands/GMCommands.h"
#include "../Addons/LootExchange/LootExchange.h"
#include "../Addons/MauExp/MauExp.h"
#include "../Addons/PvpPotion/PvpPotion.h"
#include "../Addons/RadiusDropLoot/RadiusDropLoot.h"
#include "../Addons/ReplaceLootName/ReplaceLootName.h"
#include "../Addons/ServerMemoryPatch/ServerMemoryPatch.h"
#include "../Addons/StoneHP/StoneHP.h"
#include "../Addons/VariousSettings/VariousSettings.h"

// Note: By including these headers, the CModuleRegister<T> constructors will be called
// during static initialization, automatically registering all addon modules
