#include "stdafx.h"

#include "EnchantChance.h"
#include "../../Common/Helpers/ModuleProxy.hpp"
#include "../../Common/Interfaces/ModuleInterface.h"

using namespace GameServer::Addon;

#define DllExport __declspec(dllexport)

extern "C" DllExport
NexusPro::Module::IModule* CreateModule()
{
    return ModuleProxy::CModuleProxy<CEnchantChance>::get_instance()->CreateModule();
}

extern "C" DllExport
void ReleaseModule(NexusPro::Module::IModule* pObj)
{
    ModuleProxy::CModuleProxy<CEnchantChance>::get_instance()->ReleaseModule(pObj);
}
