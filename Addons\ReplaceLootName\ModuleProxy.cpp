#include "stdafx.h"

#include "ReplaceLootName.h"
#include "../../Common/Helpers/ModuleProxy.hpp"
#include "../../Common/Interfaces/ModuleInterface.h"

using namespace GameServer::Addon;

#define DllExport __declspec(dllexport)

extern "C" DllExport
NexusPro::Module::IModule* CreateModule()
{
    return ModuleProxy::CModuleProxy<CReplaceLootName>::get_instance()->CreateModule();
}

extern "C" DllExport
void ReleaseModule(NexusPro::Module::IModule* pObj)
{
    ModuleProxy::CModuleProxy<CReplaceLootName>::get_instance()->ReleaseModule(pObj);
}
