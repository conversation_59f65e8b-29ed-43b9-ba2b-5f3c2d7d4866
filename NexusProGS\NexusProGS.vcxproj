<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="ReleaseDebug|x64">
      <Configuration>ReleaseDebug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <ProjectGuid>{2F325A98-EFAE-4DBB-932A-F9851B4DB7C4}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>NexusPro</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <TargetName>NexusPro</TargetName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDebug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDebug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDebug|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_USRDLL;YOROZUYAGS_EXPORTS;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\library\ATF\;..\library\MinHook\include\;..\library\;$(ProjectDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <CompileAsManaged>false</CompileAsManaged>
      <CompileAsWinRT>false</CompileAsWinRT>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <ControlFlowGuard>false</ControlFlowGuard>
      <AdditionalOptions>/Zm1000 /Zc:strictStrings- %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>libMinHook-$(PlatformTarget)-$(PlatformToolset)-mt.lib;winmm.lib;ws2_32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>D:\NexusPro_RFOnlineProtection_project\library\MinHook\lib;D:\NexusPro_RFOnlineProtection_project\NexusProGSLib\x64\Release;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalOptions>/FORCE:MULTIPLE /FORCE:UNRESOLVED %(AdditionalOptions)</AdditionalOptions>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseDebug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>Disabled</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_USRDLL;NEXUSPROGS_EXPORTS;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>..\library\ATF\;..\library\MinHook\include\;..\library\;$(ProjectDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <CompileAsManaged>false</CompileAsManaged>
      <CompileAsWinRT>false</CompileAsWinRT>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <ControlFlowGuard>false</ControlFlowGuard>
      <BrowseInformation>false</BrowseInformation>
      <AdditionalOptions>/Zm1000 /Zc:strictStrings- %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>libMinHook-$(PlatformTarget)-$(PlatformToolset)-mt.lib;winmm.lib;ws2_32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>D:\NexusPro_RFOnlineProtection_project\library\MinHook\lib;D:\NexusPro_RFOnlineProtection_project\NexusProGSLib\x64\Release;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalOptions>/FORCE:MULTIPLE /FORCE:UNRESOLVED %(AdditionalOptions)</AdditionalOptions>
    </Link>
    <Bscmake>
      <PreserveSbr>true</PreserveSbr>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\Common\ETypes.h" />
    <ClInclude Include="..\Common\Helpers\ModuleDllHelper.hpp" />
    <ClInclude Include="..\Common\Helpers\RapidHelper.hpp" />
    <ClInclude Include="..\Common\Helpers\SingletonHelper.hpp" />
    <ClInclude Include="..\Common\Helpers\ThreadPool.hpp" />
    <ClInclude Include="..\Common\Helpers\TimeHelper.hpp" />
    <ClInclude Include="..\Common\Helpers\zip.h" />
    <ClInclude Include="..\Common\Interfaces\ModuleInterface.h" />
    <ClInclude Include="Animus\Animus.h" />
    <ClInclude Include="AttackSystem\AttackSystem.h" />
    <ClInclude Include="AttackSystem\AttackSystemError.h" />
    <ClInclude Include="AutominePersonal\AutominePersonal.h" />
    <ClInclude Include="CheatCommand\CheatCommand.h" />
    <ClInclude Include="Common\ItemCheckHelper.h" />
    <ClInclude Include="Common\ModuleRegistry.h" />
    <ClInclude Include="Common\version.h" />
    <ClInclude Include="CrashDump\CrashDump.h" />
    <ClInclude Include="CreatePlayer\CreatePlayer.h" />
    <ClInclude Include="Guild\Guild.h" />
    <ClInclude Include="LogType\LogType.h" />
    <ClInclude Include="MainThread\MainThread.h" />
    <ClInclude Include="NetworkEx\NetworkEx.h" />
    <ClInclude Include="NpcData\NpcData.h" />
    <ClInclude Include="PcBangFavor\PcBangFavor.h" />
    <ClInclude Include="Performance\Performance.h" />
    <ClInclude Include="PlayerEx\PlayerEx.h" />
    <ClInclude Include="PlayerEx\PlayerEx_detail.h" />
    <ClInclude Include="PlayerEx\PlayerEx_PvpOrderViewDB.h" />
    <ClInclude Include="Player\Player.h" />
    <ClInclude Include="Player\PlayerChatSystem.h" />
    <ClInclude Include="Player\PlayerEquip.h" />
    <ClInclude Include="Player\PlayerGuild.h" />
    <ClInclude Include="Player\PlayerMiningOre.h" />
    <ClInclude Include="Player\PlayerMoveSystem.h" />
    <ClInclude Include="Player\PlayerPvpOrderView.h" />
    <ClInclude Include="Player\PlayerQuestMng.h" />
    <ClInclude Include="Player\PlayerTrade.h" />
    <ClInclude Include="Player\PlayerTrunk.h" />
    <ClInclude Include="Player\PlayerUnit.h" />
    <ClInclude Include="Player\PlayerViewInvisible.h" />
    <ClInclude Include="PostSystem\PostSystem.h" />
    <ClInclude Include="PotionMgr\PotionMgr.h" />
    <ClInclude Include="ReadSystemPass\ReadSystemPass.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="SaveData\Macros.h" />
    <ClInclude Include="SaveData\UpdateGeneral.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="Store\Store.h" />
    <ClInclude Include="Trap\Trap.h" />
    <ClInclude Include="UnmannedTrader\UnmannedTrader.h" />
    <ClInclude Include="UnmannedTrader\UnmannedTraderEx.h" />
    <ClInclude Include="Vote\Vote.h" />
    <ClInclude Include="NexusPro.h" />
    <ClInclude Include="AddonRegistry.h" />
    <!-- Addon Header Files -->
    <ClInclude Include="..\Addons\AccuracyEffect\AccuracyEffect.h" />
    <ClInclude Include="..\Addons\Advert\advert.h" />
    <ClInclude Include="..\Addons\BonusStart\BonusStart.h" />
    <ClInclude Include="..\Addons\ChatLog\ChatLog.h" />
    <ClInclude Include="..\Addons\DefenceFormula\DefenceFormula.h" />
    <ClInclude Include="..\Addons\EnchantChance\EnchantChance.h" />
    <ClInclude Include="..\Addons\GMCommands\GMCommands.h" />
    <ClInclude Include="..\Addons\LootExchange\LootExchange.h" />
    <ClInclude Include="..\Addons\MauExp\MauExp.h" />
    <ClInclude Include="..\Addons\PvpPotion\PvpPotion.h" />
    <ClInclude Include="..\Addons\RadiusDropLoot\RadiusDropLoot.h" />
    <ClInclude Include="..\Addons\ReplaceLootName\ReplaceLootName.h" />
    <ClInclude Include="..\Addons\ServerMemoryPatch\ServerMemoryPatch.h" />
    <ClInclude Include="..\Addons\StoneHP\StoneHP.h" />
    <ClInclude Include="..\Addons\VariousSettings\VariousSettings.h" />
    <ClInclude Include="..\Addons\VariousSettings\ISettings.h" />
    <!-- Temporarily disabled due to missing NexusProGSLib dependency -->
    <!-- <ClInclude Include="..\Addons\VariousSettings\Exchange\ExchangeSettings.h" /> -->
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\Common\Helpers\zip.cpp" />
    <ClCompile Include="Animus\Animus.cpp" />
    <ClCompile Include="AttackSystem\AttackSystem.cpp" />
    <ClCompile Include="AttackSystem\AttackSystem_CalcPoint.cpp" />
    <ClCompile Include="AutominePersonal\AutominePersonal.cpp" />
    <ClCompile Include="CheatCommand\CheatCommand.cpp" />
    <ClCompile Include="Common\ItemCheckHelper.cpp" />
    <ClCompile Include="Common\ModuleRegistry.cpp" />
    <ClCompile Include="CrashDump\CrashDump.cpp" />
    <ClCompile Include="CreatePlayer\CreatePlayer.cpp" />
    <ClCompile Include="dllmain.cpp">
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</CompileAsManaged>
      <CompileAsManaged Condition="'$(Configuration)|$(Platform)'=='ReleaseDebug|x64'">false</CompileAsManaged>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
      </PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ReleaseDebug|x64'">
      </PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="Guild\Guild.cpp" />
    <ClCompile Include="LogType\LogType.cpp" />
    <ClCompile Include="MainThread\MainThread.cpp" />
    <ClCompile Include="NetworkEx\NetworkEx.cpp" />
    <ClCompile Include="NpcData\NpcData.cpp" />
    <ClCompile Include="PcBangFavor\PcBangFavor.cpp" />
    <ClCompile Include="Performance\Performance.cpp" />
    <ClCompile Include="PlayerEx\PlayerEx.cpp" />
    <ClCompile Include="PlayerEx\PlayerEx_AttackDelay.cpp" />
    <ClCompile Include="PlayerEx\PlayerEx_Move.cpp" />
    <ClCompile Include="PlayerEx\PlayerEx_PvpOrderView.cpp" />
    <ClCompile Include="PlayerEx\PlayerEx_PvpOrderViewDB.cpp" />
    <ClCompile Include="Player\PlayerChatSystem.cpp" />
    <ClCompile Include="Player\PlayerEquip.cpp" />
    <ClCompile Include="Player\PlayerGuild.cpp" />
    <ClCompile Include="Player\Player.cpp" />
    <ClCompile Include="Player\PlayerMiningOre.cpp" />
    <ClCompile Include="Player\PlayerMoveSystem.cpp" />
    <ClCompile Include="Player\PlayerPvpOrderView.cpp" />
    <ClCompile Include="Player\PlayerQuestMng.cpp" />
    <ClCompile Include="Player\PlayerSystem.cpp" />
    <ClCompile Include="Player\PlayerTrade.cpp" />
    <ClCompile Include="Player\PlayerTrunk.cpp" />
    <ClCompile Include="Player\PlayerUnit.cpp" />
    <ClCompile Include="Player\PlayerViewInvisible.cpp" />
    <ClCompile Include="PostSystem\PostSystem.cpp" />
    <ClCompile Include="PotionMgr\PotionMgr.cpp" />
    <ClCompile Include="ReadSystemPass\ReadSystemPass.cpp" />
    <ClCompile Include="SaveData\Macros.cpp" />
    <ClCompile Include="SaveData\UpdateGeneral.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ReleaseDebug|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="Store\Store.cpp" />
    <ClCompile Include="Trap\Trap.cpp" />
    <ClCompile Include="UnmannedTrader\UnmannedTrader.cpp" />
    <ClCompile Include="Vote\Vote.cpp" />
    <ClCompile Include="NexusPro.cpp" />
    <!-- Addon Source Files -->
    <ClCompile Include="..\Addons\AccuracyEffect\AccuracyEffect.cpp" />
    <ClCompile Include="..\Addons\Advert\advert.cpp" />
    <ClCompile Include="..\Addons\BonusStart\BonusStart.cpp" />
    <ClCompile Include="..\Addons\ChatLog\ChatLog.cpp" />
    <ClCompile Include="..\Addons\DefenceFormula\DefenceFormula.cpp" />
    <ClCompile Include="..\Addons\EnchantChance\EnchantChance.cpp" />
    <ClCompile Include="..\Addons\GMCommands\GMCommands.cpp" />
    <ClCompile Include="..\Addons\LootExchange\LootExchange.cpp" />
    <ClCompile Include="..\Addons\MauExp\MauExp.cpp" />
    <ClCompile Include="..\Addons\PvpPotion\PvpPotion.cpp" />
    <ClCompile Include="..\Addons\RadiusDropLoot\RadiusDropLoot.cpp" />
    <ClCompile Include="..\Addons\ReplaceLootName\ReplaceLootName.cpp" />
    <ClCompile Include="..\Addons\ServerMemoryPatch\ServerMemoryPatch.cpp" />
    <ClCompile Include="..\Addons\StoneHP\StoneHP.cpp" />
    <ClCompile Include="..\Addons\VariousSettings\VariousSettings.cpp" />
    <!-- Temporarily disabled due to missing NexusProGSLib dependency -->
    <!-- <ClCompile Include="..\Addons\VariousSettings\Exchange\ExchangeSettings.cpp" /> -->
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\library\MinHook\build\VC15\libMinHook.vcxproj">
      <Project>{f142a341-5ee0-442d-a15f-98ae9b48dbae}</Project>
    </ProjectReference>

  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="Resource.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>